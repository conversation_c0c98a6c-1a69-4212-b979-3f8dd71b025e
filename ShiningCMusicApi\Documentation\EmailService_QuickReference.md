# Email Service Quick Reference

## Quick Setup

### 1. Database Setup
```sql
-- Run this script to create tables and initial template
EXEC [Add_Email_Templates.sql]
```

### 2. Service Injection
```csharp
// In your controller or service
[Inject] protected IEmailService EmailService { get; set; }
```

## Common Usage Patterns

### Send Schedule Ready Email
```csharp
// Simple tutor notification
var success = await EmailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName);
```

### Send Template-Based Email
```csharp
// With placeholders
var placeholders = new Dictionary<string, string>
{
    { "StudentName", "John Doe" },
    { "LessonDate", "July 25, 2025" }
};

var success = await EmailService.SendEmailFromTemplateAsync(
    "LessonReminder", 
    "<EMAIL>", 
    placeholders);
```

### Send Direct Email
```csharp
// Without template
var success = await EmailService.SendEmailAsync(
    "<EMAIL>",
    "Subject Line",
    "<h1>HTML Content</h1>",
    isHtml: true);
```

## Template Placeholders

### Available Placeholders
- `{TutorName}` - Tutor's full name
- `{StudentName}` - Student's full name
- `{LessonDate}` - Formatted lesson date
- `{LessonTime}` - Lesson time
- `{Location}` - Lesson location
- `{InstructorName}` - Instructor name

### Placeholder Syntax
```html
<!-- In template -->
<h2>Hello {TutorName}!</h2>
<p>Your lesson on {LessonDate} at {LessonTime} is confirmed.</p>
```

```csharp
// In code
var placeholders = new Dictionary<string, string>
{
    { "TutorName", tutor.TutorName },
    { "LessonDate", lesson.Date.ToString("MMMM dd, yyyy") },
    { "LessonTime", lesson.Time.ToString("h:mm tt") }
};
```

## API Endpoints Quick Reference

### Email Sending
```http
POST /api/email/send-schedule-ready/{tutorId}    # Send schedule ready email
POST /api/email/send                             # Send custom email
```

### Template Management
```http
GET    /api/emailtemplates                       # Get all templates
GET    /api/emailtemplates/{templateName}        # Get specific template
POST   /api/emailtemplates                       # Create template
PUT    /api/emailtemplates/{templateName}        # Update template
DELETE /api/emailtemplates/{templateName}        # Delete template
```

### Attachment Management
```http
GET    /api/emailtemplates/{templateName}/attachments  # Get attachments
POST   /api/emailtemplates/attachments                 # Add attachment
DELETE /api/emailtemplates/attachments/{id}            # Delete attachment
```

## Pre-configured Templates

### ScheduleReady
- **Purpose:** Notify tutors when schedule is ready
- **Placeholders:** `{TutorName}`
- **Usage:** `SendScheduleReadyEmailAsync(email, name)`

## Error Handling

### Return Values
- `true` - Email sent successfully
- `false` - Email failed to send (check logs)

### Common Failures
1. **Invalid email address** - Check email format
2. **Template not found** - Verify template name exists in database
3. **SMTP configuration** - Check appsettings.json EmailSettings
4. **Missing placeholders** - Ensure all required placeholders are provided

## Configuration

### Configuration Settings

#### Environment Variables (Recommended)
```bash
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SENDER_EMAIL=<EMAIL>
EMAIL_SENDER_PASSWORD=your-app-password
EMAIL_SENDER_NAME=Shining C Music Studio
```

#### Fallback Settings (appsettings.json)
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password",
    "SenderName": "Shining C Music Studio"
  }
}
```

## Client-Side Usage (Blazor)

### Service Injection
```csharp
[Inject] protected IEmailApiService EmailApi { get; set; }
```

### Send Email from UI
```csharp
protected async Task SendScheduleEmail(Tutor tutor)
{
    var success = await EmailApi.SendScheduleReadyEmailAsync(tutor.TutorId);
    if (success)
    {
        await DialogService.ShowSuccessAsync("Email sent successfully!");
    }
    else
    {
        await DialogService.ShowErrorAsync("Failed to send email");
    }
}
```

## Troubleshooting

### Email Not Sending
1. Check environment variables first: `EMAIL_SMTP_SERVER`, `EMAIL_SENDER_EMAIL`, etc.
2. If no environment variables, check SMTP settings in appsettings.json
3. Verify Gmail app password is correct
4. Check firewall/network restrictions on port 587
5. Review application logs for detailed error messages

### Template Not Found
1. Verify template exists: `SELECT * FROM EmailTemplates WHERE Name = 'TemplateName'`
2. Check template name spelling (case-sensitive)
3. Run `Add_Email_Templates.sql` if templates are missing

### Placeholders Not Replacing
1. Ensure placeholder names match exactly (case-sensitive)
2. Use curly brace syntax: `{PlaceholderName}`
3. Verify placeholders dictionary contains all required values

## Best Practices

### Template Design
- Always provide both HTML and plain text versions
- Use responsive HTML design
- Include unsubscribe information for marketing emails
- Test templates with various email clients

### Code Usage
- Always handle return values (true/false)
- Use appropriate logging levels
- Validate email addresses before sending
- Consider rate limiting for bulk operations

### Security
- Never expose SMTP credentials in client-side code
- Validate all user inputs
- Use authorization on all email endpoints
- Consider email content filtering for user-generated content

## Example: Complete Email Flow

```csharp
// 1. Get tutor information
var tutor = await TutorService.GetTutorAsync(tutorId);

// 2. Validate email address
if (string.IsNullOrWhiteSpace(tutor.Email))
{
    return BadRequest("Tutor email not configured");
}

// 3. Send email
var success = await EmailService.SendScheduleReadyEmailAsync(tutor.Email, tutor.TutorName);

// 4. Handle result
if (success)
{
    Logger.LogInformation("Schedule email sent to {TutorName}", tutor.TutorName);
    return Ok(new { message = "Email sent successfully" });
}
else
{
    Logger.LogError("Failed to send schedule email to {TutorName}", tutor.TutorName);
    return StatusCode(500, new { message = "Failed to send email" });
}
```
