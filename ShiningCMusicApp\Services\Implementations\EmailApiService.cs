using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services
{
    public class EmailApiService : IEmailApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;

        public EmailApiService(HttpClient httpClient, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<bool> SendScheduleReadyEmailAsync(int tutorId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.PostAsync($"{_baseUrl}/email/send-schedule-ready/{tutorId}", null);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending schedule ready email: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var request = new
                {
                    ToEmail = toEmail,
                    Subject = subject,
                    Body = body,
                    IsHtml = isHtml
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/email/send", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending email: {ex.Message}");
                return false;
            }
        }
    }
}
