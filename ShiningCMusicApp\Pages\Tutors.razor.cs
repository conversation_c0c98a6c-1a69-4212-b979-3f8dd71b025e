using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Pages;

public partial class TutorsBase : ComponentBase
{
    [Inject] protected ITutorApiService TutorApi { get; set; } = default!;
    [Inject] protected ISubjectApiService SubjectApi { get; set; } = default!;
    [Inject] protected ILessonApiService LessonApi { get; set; } = default!;
    [Inject] protected IEmailApiService EmailApi { get; set; } = default!;
    [Inject] protected IEmailTemplateApiService EmailTemplateApi { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;

    // Data properties
    protected List<Tutor> tutors = new();
    protected List<Subject> subjects = new();
    protected List<EmailTemplate> emailTemplates = new();
    protected bool isLoading = true;
    protected bool showModal = false;
    protected bool showTemplateSelectionModal = false;
    protected bool isEditMode = false;
    protected bool isSaving = false;
    protected bool isSendingEmail = false;
    protected string modalTitle = "";
    protected string templateSelectionModalTitle = "";
    protected Tutor currentTutor = new();
    protected Tutor? currentEmailTutor = null;
    protected string currentTutorSubjectName = "";
    protected string selectedTemplateName = "";
    protected bool showSubjectValidation = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            var tutorsTask = TutorApi.GetTutorsAsync();
            var subjectsTask = SubjectApi.GetSubjectsAsync();

            await Task.WhenAll(tutorsTask, subjectsTask);

            tutors = await tutorsTask;
            subjects = await subjectsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {tutors.Count} tutors and {subjects.Count} subjects");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    protected void OpenCreateModal()
    {
        currentTutor = new Tutor { Color = "#6C757D" };
        showSubjectValidation = false;
        isEditMode = false;
        modalTitle = "Create New Tutor";
        showModal = true;
    }

    protected void OpenEditModal(Tutor? tutor)
    {
        if (tutor != null)
        {
            currentTutor = new Tutor
            {
                TutorId = tutor.TutorId,
                TutorName = tutor.TutorName,
                Email = tutor.Email,
                LoginName = tutor.LoginName,
                SubjectId = tutor.SubjectId,
                Color = tutor.Color ?? "#6C757D"
            };

            // Set the subject name for readonly display
            var subject = subjects.FirstOrDefault(s => s.SubjectId == tutor.SubjectId);
            currentTutorSubjectName = subject?.SubjectName ?? "No subject assigned";

            isEditMode = true;
            modalTitle = "Edit Tutor";
            showModal = true;
        }
    }

    protected void CloseModal()
    {
        showModal = false;
        currentTutor = new();
        currentTutorSubjectName = "";
        showSubjectValidation = false;
        isSaving = false;
    }

    protected async Task SaveTutor()
    {
        showSubjectValidation = false;

        if (string.IsNullOrWhiteSpace(currentTutor.TutorName))
        {
            await DialogService.ShowWarningAsync("Tutor name is required.", "Please enter a valid tutor name before saving.");
            return;
        }

        // Validate subject for new tutor only
        if (!isEditMode)
        {
            bool hasValidationErrors = false;

            if (!currentTutor.SubjectId.HasValue || currentTutor.SubjectId <= 0)
            {
                showSubjectValidation = true;
                hasValidationErrors = true;
            }

            if (hasValidationErrors)
            {
                StateHasChanged();
                return;
            }
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await TutorApi.UpdateTutorAsync(currentTutor.TutorId, currentTutor);
            }
            else
            {
                var createdTutor = await TutorApi.CreateTutorAsync(currentTutor);
                success = createdTutor != null;
            }

            if (success)
            {
                CloseModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save tutor", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving tutor: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving tutor", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteTutor(Tutor? tutor)
    {
        if (tutor == null) return;

        try
        {
            // Check if tutor has future lessons
            var lessons = await LessonApi.GetLessonsAsync();
            var futureLessons = lessons.Where(l => l.TutorId == tutor.TutorId && l.StartTime > DateTime.Now).ToList();

            if (futureLessons.Any())
            {
                var warningMessage = $"Cannot delete tutor '{tutor.TutorName}' because they have {futureLessons.Count} upcoming lesson(s).";
                var warningDetails = "Please cancel or reschedule their future lessons first.";
                await DialogService.ShowWarningAsync(warningMessage, warningDetails, "Cannot Delete Tutor");
                return;
            }

            var message = $"Are you sure you want to delete tutor '{tutor.TutorName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Tutor");

            if (confirmed)
            {
                var success = await TutorApi.DeleteTutorAsync(tutor.TutorId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete tutor", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting tutor: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting tutor", ex.Message);
        }
    }

    protected async Task ToggleArchiveStatus(Tutor? tutor)
    {
        if (tutor == null) return;

        try
        {
            tutor.IsArchived = !tutor.IsArchived;
            var success = await TutorApi.UpdateTutorAsync(tutor.TutorId, tutor);
            
            if (success)
            {
                await LoadData();
            }
            else
            {
                // Revert the change if update failed
                tutor.IsArchived = !tutor.IsArchived;
                await DialogService.ShowErrorAsync("Failed to update tutor status", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            // Revert the change if update failed
            tutor.IsArchived = !tutor.IsArchived;
            await JSRuntime.InvokeVoidAsync("console.error", $"Error updating tutor status: {ex.Message}");
            await DialogService.ShowErrorAsync("Error updating tutor status", ex.Message);
        }
    }

    protected string GetSubjectName(int? subjectId)
    {
        if (!subjectId.HasValue) return "No Subject";
        var subject = subjects.FirstOrDefault(s => s.SubjectId == subjectId.Value);
        return subject?.SubjectName ?? "Unknown Subject";
    }

    protected string GetArchiveButtonText(bool isArchived)
    {
        return isArchived ? "Unarchive" : "Archive";
    }

    protected string GetArchiveButtonClass(bool isArchived)
    {
        return isArchived ? "btn-outline-success" : "btn-outline-warning";
    }

    protected string GetArchiveButtonIcon(bool isArchived)
    {
        return isArchived ? "bi-arrow-up-circle" : "bi-archive";
    }

    protected void ViewLessons(Tutor? tutor)
    {
        if (tutor != null)
        {
            // Navigate to lessons page with tutor filter
            Navigation.NavigateTo($"/lessons?tutorId={tutor.TutorId}");
        }
    }

    protected void OnCurrentTutorColorChanged(string newColor)
    {
        currentTutor.Color = newColor;
    }

    protected async Task SendScheduleEmail(Tutor? tutor)
    {
        if (tutor == null) return;

        try
        {
            if (string.IsNullOrWhiteSpace(tutor.Email))
            {
                await DialogService.ShowWarningAsync("No Email Address", $"Tutor '{tutor.TutorName}' does not have an email address configured.");
                return;
            }

            // Load available email templates
            emailTemplates = await EmailTemplateApi.GetTemplatesAsync();

            if (emailTemplates.Count == 0)
            {
                await DialogService.ShowWarningAsync("No Email Templates", "No email templates are available. Please create at least one email template first.");
                return;
            }
            else if (emailTemplates.Count == 1)
            {
                // Only one template exists, send it directly
                await SendEmailWithTemplate(tutor, emailTemplates[0].Name);
            }
            else
            {
                // Multiple templates exist, show selection dialog
                currentEmailTutor = tutor;
                templateSelectionModalTitle = $"Select Email Template for {tutor.TutorName}";
                selectedTemplateName = "";
                showTemplateSelectionModal = true;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error preparing email: {ex.Message}");
            await DialogService.ShowErrorAsync("Error preparing email", ex.Message);
        }
    }

    protected async Task SendEmailWithTemplate(Tutor tutor, string templateName)
    {
        try
        {
            isSendingEmail = true;
            var success = await EmailApi.SendTemplateEmailAsync(tutor.TutorId, templateName);
            if (success)
            {
                await DialogService.ShowSuccessAsync("Email Sent", $"Email has been sent to {tutor.TutorName} at {tutor.Email} using template '{templateName}'.");
            }
            else
            {
                await DialogService.ShowErrorAsync("Email Failed", "Failed to send the email. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error sending email: {ex.Message}");
            await DialogService.ShowErrorAsync("Error sending email", ex.Message);
        }
        finally
        {
            isSendingEmail = false;
        }
    }

    protected async Task SendSelectedTemplate()
    {
        if (currentEmailTutor == null || string.IsNullOrWhiteSpace(selectedTemplateName))
        {
            await DialogService.ShowWarningAsync("Template Selection Required", "Please select a template before sending the email.");
            return;
        }

        CloseTemplateSelectionModal();
        await SendEmailWithTemplate(currentEmailTutor, selectedTemplateName);
    }

    protected void CloseTemplateSelectionModal()
    {
        showTemplateSelectionModal = false;
        currentEmailTutor = null;
        selectedTemplateName = "";
        templateSelectionModalTitle = "";
    }
}
